a:51:{s:13:"687237a16b614";a:13:{s:3:"tag";s:13:"687237a16b614";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315809.211758;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.3663952350616455;}s:13:"687237a632d33";a:13:{s:3:"tag";s:13:"687237a632d33";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315813.988305;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10386824;s:14:"processingTime";d:0.3086400032043457;}s:13:"687237b12a5d9";a:13:{s:3:"tag";s:13:"687237b12a5d9";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315824.950842;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.3655850887298584;}s:13:"68723835b3d24";a:13:{s:3:"tag";s:13:"68723835b3d24";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315957.332638;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.5913801193237305;}s:13:"6872383818a59";a:13:{s:3:"tag";s:13:"6872383818a59";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315959.819452;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10390920;s:14:"processingTime";d:0.39259815216064453;}s:13:"6872385dce19f";a:13:{s:3:"tag";s:13:"6872385dce19f";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315997.563048;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424152;s:14:"processingTime";d:0.46104907989501953;}s:13:"6872385faa066";a:13:{s:3:"tag";s:13:"6872385faa066";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752315999.46556;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10389696;s:14:"processingTime";d:0.3324251174926758;}s:13:"6872386ac288d";a:13:{s:3:"tag";s:13:"6872386ac288d";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316010.514709;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.4089469909667969;}s:13:"6872386e7e779";a:13:{s:3:"tag";s:13:"6872386e7e779";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316014.26726;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424152;s:14:"processingTime";d:0.4512498378753662;}s:13:"687238782bca2";a:13:{s:3:"tag";s:13:"687238782bca2";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316023.901929;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10390920;s:14:"processingTime";d:0.3488140106201172;}s:13:"68723883a1f28";a:13:{s:3:"tag";s:13:"68723883a1f28";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316035.404494;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10899272;s:14:"processingTime";d:0.3867790699005127;}s:13:"68723897558d4";a:13:{s:3:"tag";s:13:"68723897558d4";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316055.148037;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10389696;s:14:"processingTime";d:0.2731001377105713;}s:13:"687238a877d13";a:13:{s:3:"tag";s:13:"687238a877d13";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316072.265806;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.3660728931427002;}s:13:"687238ab52470";a:13:{s:3:"tag";s:13:"687238ab52470";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316075.141742;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10390920;s:14:"processingTime";d:0.2678699493408203;}s:13:"687238ded4cfb";a:13:{s:3:"tag";s:13:"687238ded4cfb";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316126.596555;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424152;s:14:"processingTime";d:0.4153249263763428;}s:13:"687238e122a9c";a:13:{s:3:"tag";s:13:"687238e122a9c";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316128.795755;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10133408;s:14:"processingTime";d:0.38262200355529785;}s:13:"687238e4b836a";a:13:{s:3:"tag";s:13:"687238e4b836a";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=42&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316132.525317;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9265880;s:14:"processingTime";d:0.3024771213531494;}s:13:"687238f9a1041";a:13:{s:3:"tag";s:13:"687238f9a1041";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316153.329753;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10389696;s:14:"processingTime";d:0.44222497940063477;}s:13:"687238fe99501";a:13:{s:3:"tag";s:13:"687238fe99501";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316158.282164;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10133408;s:14:"processingTime";d:0.4070091247558594;}s:13:"687239013252c";a:13:{s:3:"tag";s:13:"687239013252c";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=42&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316160.901511;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9265880;s:14:"processingTime";d:0.3857901096343994;}s:13:"6872399d4310e";a:13:{s:3:"tag";s:13:"6872399d4310e";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316317.003025;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10133408;s:14:"processingTime";d:0.3022029399871826;}s:13:"687239a16b7fe";a:13:{s:3:"tag";s:13:"687239a16b7fe";s:3:"url";s:79:"http://silver/backend/worker-payment/get-worker-info?worker_id=17&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316321.176065;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9265880;s:14:"processingTime";d:0.3174300193786621;}s:13:"68723a23b9234";a:13:{s:3:"tag";s:13:"68723a23b9234";s:3:"url";s:43:"http://silver/backend/worker-payment/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316451.522376;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10133408;s:14:"processingTime";d:0.26073503494262695;}s:13:"68723a4329097";a:13:{s:3:"tag";s:13:"68723a4329097";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316482.96317;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424152;s:14:"processingTime";d:0.3484518527984619;}s:13:"68723a44cbde8";a:13:{s:3:"tag";s:13:"68723a44cbde8";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316484.62871;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.28401899337768555;}s:13:"68723a4b708a3";a:13:{s:3:"tag";s:13:"68723a4b708a3";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316491.235755;s:10:"statusCode";i:200;s:8:"sqlCount";i:255;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11726760;s:14:"processingTime";d:0.6646320819854736;}s:13:"68723a4c53cd2";a:13:{s:3:"tag";s:13:"68723a4c53cd2";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316491.961698;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.5487780570983887;}s:13:"68723a4ca766c";a:13:{s:3:"tag";s:13:"68723a4ca766c";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316492.0137;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.8495900630950928;}s:13:"68723a585523a";a:13:{s:3:"tag";s:13:"68723a585523a";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316504.081441;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.37374305725097656;}s:13:"68723b01df9c3";a:13:{s:3:"tag";s:13:"68723b01df9c3";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316673.67051;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.30521297454833984;}s:13:"68723c695c820";a:13:{s:3:"tag";s:13:"68723c695c820";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317032.921968;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.6751120090484619;}s:13:"68723c6c63217";a:13:{s:3:"tag";s:13:"68723c6c63217";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317036.115534;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.37064290046691895;}s:13:"68723d05a1ed8";a:13:{s:3:"tag";s:13:"68723d05a1ed8";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317189.39117;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424152;s:14:"processingTime";d:0.3876149654388428;}s:13:"68723d07be86d";a:13:{s:3:"tag";s:13:"68723d07be86d";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317191.564074;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.3888661861419678;}s:13:"68723d097ccfa";a:13:{s:3:"tag";s:13:"68723d097ccfa";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317193.227946;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.45343685150146484;}s:13:"68723d1001591";a:13:{s:3:"tag";s:13:"68723d1001591";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317199.751971;s:10:"statusCode";i:200;s:8:"sqlCount";i:255;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11726760;s:14:"processingTime";d:0.702826976776123;}s:13:"68723d10b5c40";a:13:{s:3:"tag";s:13:"68723d10b5c40";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317200.523379;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.35918092727661133;}s:13:"68723d110e0e5";a:13:{s:3:"tag";s:13:"68723d110e0e5";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317200.597371;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.6461620330810547;}s:13:"68723d13d7909";a:13:{s:3:"tag";s:13:"68723d13d7909";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317203.55634;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.42476820945739746;}s:13:"68723d75efbb3";a:13:{s:3:"tag";s:13:"68723d75efbb3";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317301.719828;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.3953690528869629;}s:13:"68723d792f3b5";a:13:{s:3:"tag";s:13:"68723d792f3b5";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317304.935476;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.34180212020874023;}s:13:"68723d97e30b7";a:13:{s:3:"tag";s:13:"68723d97e30b7";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317335.679199;s:10:"statusCode";i:200;s:8:"sqlCount";i:255;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11726760;s:14:"processingTime";d:0.8012409210205078;}s:13:"68723d98c897a";a:13:{s:3:"tag";s:13:"68723d98c897a";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317336.546919;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.3893899917602539;}s:13:"68723d991b27e";a:13:{s:3:"tag";s:13:"68723d991b27e";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317336.589002;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.7191250324249268;}s:13:"68723d9b6aac0";a:13:{s:3:"tag";s:13:"68723d9b6aac0";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317339.188236;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.37160301208496094;}s:13:"68723e4d1d4ad";a:13:{s:3:"tag";s:13:"68723e4d1d4ad";s:3:"url";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317516.763129;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11424752;s:14:"processingTime";d:0.5079429149627686;}s:13:"68723e4fb3295";a:13:{s:3:"tag";s:13:"68723e4fb3295";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317519.504916;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10395016;s:14:"processingTime";d:0.2961239814758301;}s:13:"68723e7a949ee";a:13:{s:3:"tag";s:13:"68723e7a949ee";s:3:"url";s:43:"http://silver/backend/worker-payment/update";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317562.504303;s:10:"statusCode";i:200;s:8:"sqlCount";i:255;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11726760;s:14:"processingTime";d:0.30007314682006836;}s:13:"68723e7b012be";a:13:{s:3:"tag";s:13:"68723e7b012be";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317562.860484;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.22292685508728027;}s:13:"68723e7b2ec0f";a:13:{s:3:"tag";s:13:"68723e7b2ec0f";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317562.845481;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.3935670852661133;}s:13:"68723e97cba48";a:13:{s:3:"tag";s:13:"68723e97cba48";s:3:"url";s:68:"http://silver/backend/worker-payment/edit?worker_id=91&month=2025-07";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752317591.688362;s:10:"statusCode";i:200;s:8:"sqlCount";i:20;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10393792;s:14:"processingTime";d:0.17939090728759766;}}