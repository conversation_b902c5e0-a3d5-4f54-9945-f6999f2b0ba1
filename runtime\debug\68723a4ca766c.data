a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:77387:"a:1:{s:8:"messages";a:106:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.08481;i:4;a:0:{}i:5;i:2655696;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.087943;i:4;a:0:{}i:5;i:2773632;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.08916;i:4;a:0:{}i:5;i:2814840;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.089175;i:4;a:0:{}i:5;i:2815216;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752316492.128153;i:4;a:0:{}i:5;i:3960416;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752316492.530302;i:4;a:0:{}i:5;i:4770904;}i:6;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752316492.559451;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6075552;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.61989;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6212976;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.656858;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6323296;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.666899;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6618064;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.684801;i:4;a:0:{}i:5;i:7308392;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752316492.690713;i:4;a:0:{}i:5;i:8092304;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752316492.691311;i:4;a:0:{}i:5;i:8117136;}i:45;a:6:{i:0;s:47:"Route requested: 'backend/worker-payment/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752316492.692356;i:4;a:0:{}i:5;i:8174128;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752316492.692367;i:4;a:0:{}i:5;i:8175760;}i:47;a:6:{i:0;s:42:"Route to run: backend/worker-payment/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752316492.695102;i:4;a:0:{}i:5;i:8304864;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.704845;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8785440;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.709604;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8792056;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1752316492.713921;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8795432;}i:55;a:6:{i:0;s:86:"Running action: app\modules\backend\controllers\WorkerPaymentController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752316492.713966;i:4;a:0:{}i:5;i:8794584;}i:56;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.714568;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8836368;}i:59;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.731517;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8987056;}i:62;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.733479;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9001576;}i:65;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.740661;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9017320;}i:68;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.746602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9028840;}i:71;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.748198;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9032504;}i:74;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.749747;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9035528;}i:77;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.751392;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9040608;}i:80;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.752866;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9045688;}i:83;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.754542;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9056760;}i:86;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.756313;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9058856;}i:89;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.758033;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9067120;}i:92;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.759521;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9070520;}i:95;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.760645;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9075600;}i:98;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76202;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9080680;}i:101;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.763533;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9085760;}i:104;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.765053;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9090840;}i:107;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.766526;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9095920;}i:110;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.768042;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9101000;}i:113;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.769587;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9106080;}i:116;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.771135;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9111160;}i:119;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.772627;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9121872;}i:122;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.774379;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9126952;}i:125;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77568;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9135216;}i:128;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.777105;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9138616;}i:131;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.778535;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9147792;}i:134;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.780045;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9152872;}i:137;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.78173;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9157952;}i:140;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.783588;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9163032;}i:143;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.784911;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9168112;}i:146;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.786387;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9173192;}i:149;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.787866;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9178272;}i:152;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.789257;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9183352;}i:155;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.790966;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9188432;}i:158;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.792699;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9193512;}i:161;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.7944;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9198592;}i:164;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.796117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9203672;}i:167;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.797324;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9208752;}i:170;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.798561;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9213832;}i:173;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.799538;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9218912;}i:176;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.800555;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9223992;}i:179;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.801576;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9229072;}i:182;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.802506;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9234152;}i:185;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.803197;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9239232;}i:188;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.804213;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9244312;}i:191;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.805374;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9249392;}i:194;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.806416;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9254472;}i:197;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.807486;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9259552;}i:200;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808365;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9264632;}i:203;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.809245;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9269712;}i:206;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.810328;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9274792;}i:209;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.811844;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9279872;}i:212;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.813361;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9284952;}i:215;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.814808;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9290032;}i:218;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.815975;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9295112;}i:221;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.817087;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9300192;}i:224;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.818145;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9305272;}i:227;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.81976;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9310352;}i:230;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.821521;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9315432;}i:233;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.823291;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9320512;}i:236;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.825199;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9325592;}i:239;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.82683;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9330672;}i:242;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.828715;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9335752;}i:245;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.830525;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9340832;}i:248;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.832146;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9345912;}i:251;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.833631;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9350992;}i:254;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.835412;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9356072;}i:257;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.837137;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9369344;}i:260;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.839072;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9374424;}i:263;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.840823;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9379504;}i:266;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.842425;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9384584;}i:269;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.843822;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9389664;}i:272;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.845577;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9394744;}i:275;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.847586;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9399824;}i:278;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.84905;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9404904;}i:281;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.850931;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9409984;}i:284;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.852528;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9415064;}i:287;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.853916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9420144;}i:290;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.855454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9425224;}i:293;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.856788;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9430304;}i:296;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.858117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9435384;}i:299;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.859746;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9444560;}i:302;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.86151;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9449640;}i:305;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.862817;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9454720;}i:308;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.864311;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9459800;}i:311;a:6:{i:0;s:98:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\worker-payment\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1752316492.869218;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:102;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9589616;}}}";s:9:"profiling";s:149263:"a:3:{s:6:"memory";i:10898048;s:4:"time";d:0.8495900630950928;s:8:"messages";a:182:{i:7;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752316492.559508;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6076680;}i:8;a:6:{i:0;s:74:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver_test_1";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752316492.618074;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6078616;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.619954;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214552;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.655126;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6229912;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.656904;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6324784;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.661025;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6326344;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.66695;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6619408;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.671613;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6622416;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.704892;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8788048;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.708887;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8790208;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.709634;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8794664;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.71306;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8796760;}i:57;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.714634;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8843904;}i:58;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.728293;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8924744;}i:60;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.731592;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8990144;}i:61;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.732923;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8996120;}i:63;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.733532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9003440;}i:64;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.740047;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9019136;}i:66;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.740698;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9019184;}i:67;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.745071;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9022008;}i:69;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.746654;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9031928;}i:70;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.7476;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9035424;}i:72;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.74823;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9035592;}i:73;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.749199;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9037776;}i:75;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.749776;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9038616;}i:76;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.750942;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9040800;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.751426;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9043696;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.752326;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9045880;}i:81;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.752898;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9048776;}i:82;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.753905;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9053264;}i:84;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.754573;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9059848;}i:85;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.755759;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9062032;}i:87;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.756345;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9061944;}i:88;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.757438;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9065440;}i:90;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.758067;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9070208;}i:91;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.758918;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9072392;}i:93;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.759557;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9073608;}i:94;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9075792;}i:96;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.760681;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9078688;}i:97;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.761635;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9080872;}i:99;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.762052;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9083768;}i:100;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.762989;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9085952;}i:102;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.763565;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9088848;}i:103;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76452;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9091032;}i:105;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76509;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9093928;}i:106;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.766003;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9096112;}i:108;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.766557;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9099008;}i:109;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.767523;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9101192;}i:111;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.768075;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9104088;}i:112;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.769005;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9106272;}i:114;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.769619;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9109168;}i:115;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.770565;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9111352;}i:117;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9114248;}i:118;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.772077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9116432;}i:120;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.772664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9124960;}i:121;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.773691;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9127144;}i:123;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.774412;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9130040;}i:124;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.775117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9133536;}i:126;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.775712;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9138304;}i:127;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77657;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9140488;}i:129;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77714;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9145800;}i:130;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.778071;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9147984;}i:132;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.778579;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9150880;}i:133;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.779306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9153064;}i:135;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.780083;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9155960;}i:136;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.780965;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9158144;}i:138;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.781791;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9161040;}i:139;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.782865;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9163224;}i:141;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.783635;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9166120;}i:142;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.784392;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9168304;}i:144;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.784975;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9171200;}i:145;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.785902;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9173384;}i:147;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.786422;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9176280;}i:148;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.787354;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9178464;}i:150;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.78791;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9181360;}i:151;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.788704;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9183544;}i:153;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.789307;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9186440;}i:154;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.790273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9188624;}i:156;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.791029;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9191520;}i:157;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.792077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9193704;}i:159;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.792753;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9196600;}i:160;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.793843;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9198784;}i:162;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.794448;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9201680;}i:163;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.795382;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9203864;}i:165;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.796171;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9206760;}i:166;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.796858;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9208944;}i:168;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.797371;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9211840;}i:169;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.798165;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9214024;}i:171;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.7986;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9216920;}i:172;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.799194;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9219104;}i:174;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.799574;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222000;}i:175;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.800166;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9224184;}i:177;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.800587;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9227080;}i:178;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.801257;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9229264;}i:180;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.801608;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9232160;}i:181;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.80219;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9234344;}i:183;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.802534;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9237240;}i:184;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.802932;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9239424;}i:186;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.803225;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9242320;}i:187;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.803756;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9244472;}i:189;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.80425;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9247400;}i:190;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.804934;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9249584;}i:192;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.805413;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9252480;}i:193;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.805986;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9254664;}i:195;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.806454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9257560;}i:196;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.807084;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9259744;}i:198;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.807515;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9262640;}i:199;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808031;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9264824;}i:201;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808391;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9267720;}i:202;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808879;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9269904;}i:204;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.809279;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9272800;}i:205;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.809863;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9274984;}i:207;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.810369;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277880;}i:208;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.811313;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9280064;}i:210;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.811885;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9282960;}i:211;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.812775;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9285144;}i:213;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.81344;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9288040;}i:214;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.814308;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9290224;}i:216;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.814842;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293120;}i:217;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.815537;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9295272;}i:219;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.816005;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298200;}i:220;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.816639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9300384;}i:222;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.817126;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9303280;}i:223;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.817664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9305464;}i:225;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.818174;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9308360;}i:226;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.81913;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9310544;}i:228;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.819805;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9313440;}i:229;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.820829;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9315624;}i:231;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.821627;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9318520;}i:232;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.822602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9320704;}i:234;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.823323;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9323600;}i:235;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.824342;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9325784;}i:237;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.825234;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9328680;}i:238;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.82623;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9330864;}i:240;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.826861;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9333760;}i:241;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.827938;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9335944;}i:243;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.828773;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9338840;}i:244;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.82983;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9341024;}i:246;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.830556;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9343920;}i:247;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.831468;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9346104;}i:249;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.832178;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349000;}i:250;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9351184;}i:252;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.833737;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9354080;}i:253;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.834575;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9356264;}i:255;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.835447;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9359160;}i:256;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83648;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9361344;}i:258;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.837274;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9372432;}i:259;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83846;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9374616;}i:261;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9377512;}i:262;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.840297;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9379696;}i:264;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.840852;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9382592;}i:265;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.841944;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9384776;}i:267;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.842458;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9387672;}i:268;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.843342;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9389856;}i:270;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.84385;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9392752;}i:271;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.844913;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9394936;}i:273;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.845625;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9397832;}i:274;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.846868;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9400016;}i:276;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.847619;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9402912;}i:277;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.848502;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9405096;}i:279;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.849083;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9407992;}i:280;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.850211;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9410176;}i:282;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.850991;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9413072;}i:283;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.852015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9415256;}i:285;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.852562;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9418152;}i:286;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.853468;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9420336;}i:288;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.853956;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9423232;}i:289;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.854926;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9425416;}i:291;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.855484;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9428312;}i:292;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.856306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9430496;}i:294;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.856816;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9433392;}i:295;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.857632;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9435576;}i:297;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.858149;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9438472;}i:298;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.859037;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9440656;}i:300;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.859804;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9447648;}i:301;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.861014;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9449832;}i:303;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.861541;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9452728;}i:304;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.862331;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9454912;}i:306;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.862858;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9457808;}i:307;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.863785;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9459992;}i:309;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.864343;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9462888;}i:310;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.865252;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9465072;}}}";s:2:"db";s:148429:"a:1:{s:8:"messages";a:180:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.619954;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6214552;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.655126;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6229912;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.656904;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6324784;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.661025;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6326344;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.66695;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6619408;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.671613;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6622416;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.704892;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8788048;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.708887;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8790208;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.709634;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8794664;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.71306;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8796760;}i:57;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.714634;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8843904;}i:58;a:6:{i:0;s:3170:"SELECT 
            w.id as worker_id,
            w.full_name,
            p.name as position,
            ws.amount as salary,
            
            -- Бонус за месяц
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) as bonus,
            
            -- Отпускные за месяц
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) as vacation_pay,
            
            -- Аванс за месяц
            COALESCE(SUM(CASE WHEN wf.type = 2 AND wf.month = '2025-07' THEN wf.amount END), 0) as advance,
            
            -- Оплата по пластиковой карте (зарплата безналичная)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) as card_payment,
            
            -- Оплата наличными (зарплата наличная)
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) as cash_payment,
            
            -- Долг (общий долг минус все погашения)
            GREATEST(COALESCE(wd.total_debt, 0) - COALESCE(debt_payments.total_payments, 0), 0) as debt,
            
            -- Удержания долга в этом месяце
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as debt_deduction,
            
            -- Получено на руки за месяц (фактически выплачено работнику)
            COALESCE(SUM(CASE WHEN wf.type = 1 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 8 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 3 AND wf.month = '2025-07' THEN wf.amount END), 0) + 
            COALESCE(SUM(CASE WHEN wf.type = 7 AND wf.month = '2025-07' THEN wf.amount END), 0) - 
            COALESCE(SUM(CASE WHEN wf.type = 6 AND wf.month = '2025-07' THEN wf.amount END), 0) as received_amount
            
        FROM worker w
        LEFT JOIN position p ON p.id = w.position_id
        LEFT JOIN worker_salary ws ON ws.worker_id = w.id AND ws.end_date = '9999-12-31'
        LEFT JOIN worker_finances wf ON wf.worker_id = w.id AND wf.deleted_at IS NULL
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_debt
            FROM worker_debt 
            WHERE deleted_at IS NULL AND (status IS NULL OR status = false)
            GROUP BY worker_id
        ) wd ON wd.worker_id = w.id
        LEFT JOIN (
            SELECT 
                worker_id,
                SUM(amount) as total_payments
            FROM worker_finances 
            WHERE type = '6' AND deleted_at IS NULL
            GROUP BY worker_id
        ) debt_payments ON debt_payments.worker_id = w.id
        WHERE w.deleted_at IS NULL
        GROUP BY w.id, w.full_name, p.name, ws.amount, wd.total_debt, debt_payments.total_payments
        ORDER BY w.full_name";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.728293;i:4;a:1:{i:0;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:90;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8924744;}i:60;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.731592;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8990144;}i:61;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=91) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.732923;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:8996120;}i:63;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.733532;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9003440;}i:64;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'worker_finances'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.740047;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9019136;}i:66;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.740698;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9019184;}i:67;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='worker_finances'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.745071;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9022008;}i:69;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.746654;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9031928;}i:70;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=87) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.7476;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9035424;}i:72;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.74823;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9035592;}i:73;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=17) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.749199;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9037776;}i:75;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.749776;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9038616;}i:76;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=16) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.750942;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9040800;}i:78;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.751426;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9043696;}i:79;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=52) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.752326;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9045880;}i:81;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.752898;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9048776;}i:82;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=59) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.753905;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9053264;}i:84;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.754573;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9059848;}i:85;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=63) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.755759;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9062032;}i:87;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.756345;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9061944;}i:88;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=36) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.757438;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9065440;}i:90;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.758067;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9070208;}i:91;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=21) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.758918;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9072392;}i:93;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.759557;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9073608;}i:94;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=41) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9075792;}i:96;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.760681;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9078688;}i:97;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=18) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.761635;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9080872;}i:99;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.762052;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9083768;}i:100;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=51) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.762989;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9085952;}i:102;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.763565;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9088848;}i:103;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=15) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76452;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9091032;}i:105;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.76509;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9093928;}i:106;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=70) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.766003;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9096112;}i:108;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.766557;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9099008;}i:109;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=90) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.767523;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9101192;}i:111;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.768075;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9104088;}i:112;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=42) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.769005;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9106272;}i:114;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.769619;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9109168;}i:115;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=64) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.770565;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9111352;}i:117;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9114248;}i:118;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=75) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.772077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9116432;}i:120;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.772664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9124960;}i:121;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=73) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.773691;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9127144;}i:123;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.774412;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9130040;}i:124;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=76) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.775117;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9133536;}i:126;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.775712;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9138304;}i:127;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=71) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77657;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9140488;}i:129;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.77714;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9145800;}i:130;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=72) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.778071;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9147984;}i:132;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.778579;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9150880;}i:133;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=77) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.779306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9153064;}i:135;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.780083;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9155960;}i:136;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=54) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.780965;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9158144;}i:138;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.781791;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9161040;}i:139;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=53) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.782865;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9163224;}i:141;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.783635;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9166120;}i:142;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=74) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.784392;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9168304;}i:144;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.784975;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9171200;}i:145;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=22) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.785902;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9173384;}i:147;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.786422;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9176280;}i:148;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=40) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.787354;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9178464;}i:150;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.78791;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9181360;}i:151;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=43) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.788704;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9183544;}i:153;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.789307;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9186440;}i:154;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=65) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.790273;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9188624;}i:156;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.791029;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9191520;}i:157;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=60) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.792077;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9193704;}i:159;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.792753;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9196600;}i:160;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=11) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.793843;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9198784;}i:162;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.794448;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9201680;}i:163;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=61) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.795382;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9203864;}i:165;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.796171;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9206760;}i:166;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=79) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.796858;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9208944;}i:168;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.797371;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9211840;}i:169;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=69) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.798165;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9214024;}i:171;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.7986;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9216920;}i:172;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=85) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.799194;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9219104;}i:174;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.799574;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9222000;}i:175;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=55) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.800166;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9224184;}i:177;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.800587;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9227080;}i:178;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=24) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.801257;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9229264;}i:180;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.801608;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9232160;}i:181;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=39) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.80219;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9234344;}i:183;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.802534;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9237240;}i:184;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=14) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.802932;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9239424;}i:186;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.803225;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9242320;}i:187;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=8) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.803756;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9244472;}i:189;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.80425;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9247400;}i:190;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=19) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.804934;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9249584;}i:192;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.805413;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9252480;}i:193;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=44) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.805986;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9254664;}i:195;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.806454;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9257560;}i:196;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=56) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.807084;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9259744;}i:198;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.807515;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9262640;}i:199;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=31) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808031;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9264824;}i:201;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808391;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9267720;}i:202;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=32) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.808879;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9269904;}i:204;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.809279;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9272800;}i:205;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=86) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.809863;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9274984;}i:207;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.810369;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9277880;}i:208;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=83) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.811313;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9280064;}i:210;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.811885;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9282960;}i:211;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=88) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.812775;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9285144;}i:213;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.81344;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9288040;}i:214;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=80) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.814308;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9290224;}i:216;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.814842;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9293120;}i:217;a:6:{i:0;s:106:"SELECT * FROM "worker_finances" WHERE (("worker_id"=9) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.815537;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9295272;}i:219;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.816005;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9298200;}i:220;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=66) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.816639;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9300384;}i:222;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.817126;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9303280;}i:223;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=81) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.817664;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9305464;}i:225;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.818174;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9308360;}i:226;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=12) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.81913;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9310544;}i:228;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.819805;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9313440;}i:229;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=78) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.820829;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9315624;}i:231;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.821627;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9318520;}i:232;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=49) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.822602;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9320704;}i:234;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.823323;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9323600;}i:235;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=28) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.824342;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9325784;}i:237;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.825234;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9328680;}i:238;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=33) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.82623;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9330864;}i:240;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.826861;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9333760;}i:241;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=34) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.827938;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9335944;}i:243;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.828773;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9338840;}i:244;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=27) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.82983;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9341024;}i:246;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.830556;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9343920;}i:247;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=23) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.831468;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9346104;}i:249;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.832178;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9349000;}i:250;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=82) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9351184;}i:252;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.833737;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9354080;}i:253;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=29) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.834575;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9356264;}i:255;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.835447;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9359160;}i:256;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=67) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83648;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9361344;}i:258;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.837274;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9372432;}i:259;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=13) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83846;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9374616;}i:261;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.83916;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9377512;}i:262;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=37) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.840297;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9379696;}i:264;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.840852;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9382592;}i:265;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=68) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.841944;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9384776;}i:267;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.842458;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9387672;}i:268;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=25) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.843342;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9389856;}i:270;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.84385;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9392752;}i:271;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=20) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.844913;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9394936;}i:273;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.845625;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9397832;}i:274;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=26) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.846868;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9400016;}i:276;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.847619;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9402912;}i:277;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=10) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.848502;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9405096;}i:279;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.849083;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9407992;}i:280;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=57) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.850211;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9410176;}i:282;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.850991;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9413072;}i:283;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=84) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.852015;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9415256;}i:285;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.852562;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9418152;}i:286;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=50) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.853468;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9420336;}i:288;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.853956;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9423232;}i:289;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=62) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.854926;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9425416;}i:291;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.855484;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9428312;}i:292;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=35) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.856306;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9430496;}i:294;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.856816;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9433392;}i:295;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=45) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.857632;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9435576;}i:297;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.858149;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9438472;}i:298;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=38) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.859037;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9440656;}i:300;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.859804;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9447648;}i:301;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=48) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.861014;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9449832;}i:303;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.861541;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9452728;}i:304;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=47) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.862331;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9454912;}i:306;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.862858;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9457808;}i:307;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=46) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.863785;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9459992;}i:309;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.864343;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9462888;}i:310;a:6:{i:0;s:107:"SELECT * FROM "worker_finances" WHERE (("worker_id"=58) AND ("month"='2025-07')) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752316492.865252;i:4;a:2:{i:0;a:5:{s:4:"file";s:101:"D:\OSPanel\domains\silverzavod\modules\backend\services\worker_payment\WorkerPaymentUpdateService.php";s:4:"line";i:282;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:86:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\WorkerPaymentController.php";s:4:"line";i:95;s:8:"function";s:19:"getExistingPayments";s:5:"class";s:70:"app\modules\backend\services\worker_payment\WorkerPaymentUpdateService";s:4:"type";s:2:"->";}}i:5;i:9465072;}}}";s:5:"event";s:25307:"a:144:{i:0;a:5:{s:4:"time";d:1752316492.552104;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752316492.618059;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752316492.671985;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1752316492.672047;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1752316492.680582;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1752316492.69167;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752316492.69554;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752316492.695557;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1752316492.700954;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1752316492.701;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1752316492.701021;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1752316492.70104;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1752316492.701055;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1752316492.701078;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1752316492.701089;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1752316492.701158;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:55:"app\modules\backend\controllers\WorkerPaymentController";}i:16;a:5:{s:4:"time";d:1752316492.730056;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752316492.733368;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:18;a:5:{s:4:"time";d:1752316492.745494;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:19;a:5:{s:4:"time";d:1752316492.745529;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:20;a:5:{s:4:"time";d:1752316492.745553;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:21;a:5:{s:4:"time";d:1752316492.745577;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:22;a:5:{s:4:"time";d:1752316492.745599;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:23;a:5:{s:4:"time";d:1752316492.74562;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:24;a:5:{s:4:"time";d:1752316492.745644;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:25;a:5:{s:4:"time";d:1752316492.745653;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:26;a:5:{s:4:"time";d:1752316492.745661;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:27;a:5:{s:4:"time";d:1752316492.745669;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:28;a:5:{s:4:"time";d:1752316492.745677;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:29;a:5:{s:4:"time";d:1752316492.745685;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:30;a:5:{s:4:"time";d:1752316492.745693;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:31;a:5:{s:4:"time";d:1752316492.746316;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:32;a:5:{s:4:"time";d:1752316492.747952;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:33;a:5:{s:4:"time";d:1752316492.748001;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:34;a:5:{s:4:"time";d:1752316492.74802;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:35;a:5:{s:4:"time";d:1752316492.748025;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:36;a:5:{s:4:"time";d:1752316492.748069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:1752316492.749621;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:38;a:5:{s:4:"time";d:1752316492.751269;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:39;a:5:{s:4:"time";d:1752316492.752736;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:40;a:5:{s:4:"time";d:1752316492.75427;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:41;a:5:{s:4:"time";d:1752316492.754317;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:42;a:5:{s:4:"time";d:1752316492.754336;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:43;a:5:{s:4:"time";d:1752316492.754352;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:44;a:5:{s:4:"time";d:1752316492.754368;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:45;a:5:{s:4:"time";d:1752316492.754374;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:46;a:5:{s:4:"time";d:1752316492.754379;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:47;a:5:{s:4:"time";d:1752316492.754384;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:48;a:5:{s:4:"time";d:1752316492.754427;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:49;a:5:{s:4:"time";d:1752316492.756185;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:50;a:5:{s:4:"time";d:1752316492.757818;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:51;a:5:{s:4:"time";d:1752316492.757861;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:52;a:5:{s:4:"time";d:1752316492.75788;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:53;a:5:{s:4:"time";d:1752316492.757886;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:54;a:5:{s:4:"time";d:1752316492.757922;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:55;a:5:{s:4:"time";d:1752316492.759373;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:56;a:5:{s:4:"time";d:1752316492.760497;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:57;a:5:{s:4:"time";d:1752316492.761897;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:58;a:5:{s:4:"time";d:1752316492.763404;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:59;a:5:{s:4:"time";d:1752316492.764925;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:60;a:5:{s:4:"time";d:1752316492.766391;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:61;a:5:{s:4:"time";d:1752316492.767912;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:62;a:5:{s:4:"time";d:1752316492.769455;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:63;a:5:{s:4:"time";d:1752316492.770997;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:64;a:5:{s:4:"time";d:1752316492.772497;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:65;a:5:{s:4:"time";d:1752316492.774226;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:66;a:5:{s:4:"time";d:1752316492.77539;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:67;a:5:{s:4:"time";d:1752316492.775468;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:68;a:5:{s:4:"time";d:1752316492.775498;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:69;a:5:{s:4:"time";d:1752316492.775505;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:41:"app\modules\backend\models\WorkerFinances";}i:70;a:5:{s:4:"time";d:1752316492.775558;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:71;a:5:{s:4:"time";d:1752316492.776974;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:72;a:5:{s:4:"time";d:1752316492.778367;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:73;a:5:{s:4:"time";d:1752316492.779797;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:74;a:5:{s:4:"time";d:1752316492.781433;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:75;a:5:{s:4:"time";d:1752316492.783394;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:76;a:5:{s:4:"time";d:1752316492.784668;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:77;a:5:{s:4:"time";d:1752316492.786241;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:78;a:5:{s:4:"time";d:1752316492.787699;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:79;a:5:{s:4:"time";d:1752316492.789067;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:80;a:5:{s:4:"time";d:1752316492.790715;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:81;a:5:{s:4:"time";d:1752316492.792486;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:82;a:5:{s:4:"time";d:1752316492.794189;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:83;a:5:{s:4:"time";d:1752316492.795894;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:84;a:5:{s:4:"time";d:1752316492.797121;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:85;a:5:{s:4:"time";d:1752316492.79841;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:86;a:5:{s:4:"time";d:1752316492.799394;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:87;a:5:{s:4:"time";d:1752316492.80043;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:88;a:5:{s:4:"time";d:1752316492.801447;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:89;a:5:{s:4:"time";d:1752316492.802384;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:90;a:5:{s:4:"time";d:1752316492.803083;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:91;a:5:{s:4:"time";d:1752316492.804064;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:92;a:5:{s:4:"time";d:1752316492.805232;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:93;a:5:{s:4:"time";d:1752316492.806274;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:94;a:5:{s:4:"time";d:1752316492.807374;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:95;a:5:{s:4:"time";d:1752316492.80826;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:96;a:5:{s:4:"time";d:1752316492.809103;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:97;a:5:{s:4:"time";d:1752316492.810182;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:98;a:5:{s:4:"time";d:1752316492.811665;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:99;a:5:{s:4:"time";d:1752316492.813158;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:100;a:5:{s:4:"time";d:1752316492.814637;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:101;a:5:{s:4:"time";d:1752316492.815852;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:102;a:5:{s:4:"time";d:1752316492.816942;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:103;a:5:{s:4:"time";d:1752316492.818027;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:104;a:5:{s:4:"time";d:1752316492.819596;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:105;a:5:{s:4:"time";d:1752316492.821332;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:106;a:5:{s:4:"time";d:1752316492.823156;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:107;a:5:{s:4:"time";d:1752316492.825066;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:108;a:5:{s:4:"time";d:1752316492.826685;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:109;a:5:{s:4:"time";d:1752316492.828337;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:110;a:5:{s:4:"time";d:1752316492.830396;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:111;a:5:{s:4:"time";d:1752316492.831921;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:112;a:5:{s:4:"time";d:1752316492.833458;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:113;a:5:{s:4:"time";d:1752316492.835107;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:114;a:5:{s:4:"time";d:1752316492.836902;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:115;a:5:{s:4:"time";d:1752316492.838886;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:116;a:5:{s:4:"time";d:1752316492.840689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:117;a:5:{s:4:"time";d:1752316492.84231;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:118;a:5:{s:4:"time";d:1752316492.843709;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:119;a:5:{s:4:"time";d:1752316492.845397;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:120;a:5:{s:4:"time";d:1752316492.847426;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:121;a:5:{s:4:"time";d:1752316492.848836;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:122;a:5:{s:4:"time";d:1752316492.850582;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:123;a:5:{s:4:"time";d:1752316492.852389;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:124;a:5:{s:4:"time";d:1752316492.853742;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:125;a:5:{s:4:"time";d:1752316492.855336;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:126;a:5:{s:4:"time";d:1752316492.856674;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:1752316492.858001;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:128;a:5:{s:4:"time";d:1752316492.859521;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:129;a:5:{s:4:"time";d:1752316492.861388;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:130;a:5:{s:4:"time";d:1752316492.862654;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:131;a:5:{s:4:"time";d:1752316492.86418;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:132;a:5:{s:4:"time";d:1752316492.869201;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:133;a:5:{s:4:"time";d:1752316492.892214;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:134;a:5:{s:4:"time";d:1752316492.89497;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:135;a:5:{s:4:"time";d:1752316492.895052;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:136;a:5:{s:4:"time";d:1752316492.895071;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:137;a:5:{s:4:"time";d:1752316492.898016;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:138;a:5:{s:4:"time";d:1752316492.898078;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:139;a:5:{s:4:"time";d:1752316492.898097;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:140;a:5:{s:4:"time";d:1752316492.899985;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:141;a:5:{s:4:"time";d:1752316492.900008;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:142;a:5:{s:4:"time";d:1752316492.901471;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:143;a:5:{s:4:"time";d:1752316492.902161;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752316492.056066;s:3:"end";d:1752316492.910497;s:6:"memory";i:10898048;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4873:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692186;i:4;a:0:{}i:5;i:8155496;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692206;i:4;a:0:{}i:5;i:8156248;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692215;i:4;a:0:{}i:5;i:8157000;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692222;i:4;a:0:{}i:5;i:8157752;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.69223;i:4;a:0:{}i:5;i:8158504;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692236;i:4;a:0:{}i:5;i:8159256;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692242;i:4;a:0:{}i:5;i:8160008;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692248;i:4;a:0:{}i:5;i:8160760;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692255;i:4;a:0:{}i:5;i:8161512;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692263;i:4;a:0:{}i:5;i:8162264;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.69227;i:4;a:0:{}i:5;i:8163016;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692277;i:4;a:0:{}i:5;i:8163768;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692282;i:4;a:0:{}i:5;i:8165800;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692287;i:4;a:0:{}i:5;i:8166552;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692291;i:4;a:0:{}i:5;i:8167304;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692297;i:4;a:0:{}i:5;i:8168056;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692301;i:4;a:0:{}i:5;i:8168808;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692306;i:4;a:0:{}i:5;i:8169560;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.69231;i:4;a:0:{}i:5;i:8170312;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692315;i:4;a:0:{}i:5;i:8171064;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692319;i:4;a:0:{}i:5;i:8171816;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692324;i:4;a:0:{}i:5;i:8172568;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752316492.692342;i:4;a:0:{}i:5;i:8174640;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752316492.692347;i:4;a:0:{}i:5;i:8174480;}}s:5:"route";s:28:"backend/worker-payment/index";s:6:"action";s:70:"app\modules\backend\controllers\WorkerPaymentController::actionIndex()";}";s:7:"request";s:9960:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:13:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:16:"x-pjax-container";s:25:"#worker-payment-grid-pjax";s:6:"x-pjax";s:4:"true";s:12:"x-csrf-token";s:88:"68Zsl7DGZT3XsPNlvT7zYANoM9bdRQkhnZwZkgYkbw6a8Di61LIDd7j6nArTep42VQN-mKQAYXXK1SjZfBAsdA==";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:22:"text/html, */*; q=0.01";s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:7:"referer";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:10:"X-Pjax-Url";s:595:"/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68723a4ca766c";s:16:"X-Debug-Duration";s:3:"846";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68723a4ca766c";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Mon, 11-Aug-2025 10:34:52 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:28:"backend/worker-payment/index";s:6:"action";s:70:"app\modules\backend\controllers\WorkerPaymentController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:1;s:6:"isPjax";b:1;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:43:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:21:"HTTP_X_PJAX_CONTAINER";s:25:"#worker-payment-grid-pjax";s:11:"HTTP_X_PJAX";s:4:"true";s:17:"HTTP_X_CSRF_TOKEN";s:88:"68Zsl7DGZT3XsPNlvT7zYANoM9bdRQkhnZwZkgYkbw6a8Di61LIDd7j6nArTep42VQN-mKQAYXXK1SjZfBAsdA==";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:22:"text/html, */*; q=0.01";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:12:"HTTP_REFERER";s:574:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=tg16mblupj6u875l5vqjrkigi7pqr8f3; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"56747";s:12:"REDIRECT_URL";s:29:"/backend/worker-payment/index";s:21:"REDIRECT_QUERY_STRING";s:565:"worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:565:"worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:11:"REQUEST_URI";s:595:"/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752316492.0137;s:12:"REQUEST_TIME";i:1752316492;}s:3:"GET";a:4:{s:9:"worker_id";s:2:"91";s:5:"month";s:7:"2025-07";s:13:"payment_types";a:6:{i:1;a:2:{s:7:"methods";a:2:{i:0;s:1:"1";i:1;s:1:"4";}s:6:"amount";s:11:"4 177 242";}i:3;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:4;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:5;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:7;a:2:{s:7:"methods";a:1:{i:0;s:1:"1";}s:6:"amount";s:6:"100000";}i:6;a:1:{s:6:"amount";s:6:"100000";}}s:5:"_pjax";s:25:"#worker-payment-grid-pjax";}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"tg16mblupj6u875l5vqjrkigi7pqr8f3";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"5de5c8623f8b1f477dee3e2ed0014819171dc78456feef3a897ed3d5ed083c16a:2:{i:0;s:5:"_csrf";i:1;s:32:"q6T-dtfJoJoonDmVVkMNyEhTWI1Kz4Cz";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1752323692;s:13:"last_activity";i:1752316492;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'Am4PA_XsYEchREdnLU19DkEKnev9Tqn03HNbrXNd'";s:8:"password";s:62:"'$2y$13$0eRlRvvyoXFPe1EZQr1a1OJtiuhoGEzSLGqLV0XWtF3ZVbwKTSkjO'";s:10:"created_at";s:21:"'2025-02-25 12:53:18'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:1953:"a:4:{s:26:"app\assets\DataTablesAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:3:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";i:2;s:35:"yii\bootstrap4\BootstrapPluginAsset";}s:2:"js";a:3:{i:0;s:73:"https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js";i:1;s:61:"https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js";i:2;s:65:"https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js";}s:3:"css";a:1:{i:0;s:67:"https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap4\BootstrapAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap4\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:62:"D:\OSPanel\domains\silverzavod\vendor/npm-asset/bootstrap/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\c3f9d53c";s:7:"baseUrl";s:16:"/assets/c3f9d53c";s:7:"depends";a:2:{i:0;s:19:"yii\web\JqueryAsset";i:1;s:29:"yii\bootstrap4\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68723a4ca766c";s:3:"url";s:608:"http://silver/backend/worker-payment/index?worker_id=91&month=2025-07&payment_types%5B1%5D%5Bmethods%5D%5B%5D=1&payment_types%5B1%5D%5Bmethods%5D%5B%5D=4&payment_types%5B1%5D%5Bamount%5D=4%C2%A0177%C2%A0242&payment_types%5B3%5D%5Bmethods%5D%5B%5D=1&payment_types%5B3%5D%5Bamount%5D=100000&payment_types%5B4%5D%5Bmethods%5D%5B%5D=1&payment_types%5B4%5D%5Bamount%5D=100000&payment_types%5B5%5D%5Bmethods%5D%5B%5D=1&payment_types%5B5%5D%5Bamount%5D=100000&payment_types%5B7%5D%5Bmethods%5D%5B%5D=1&payment_types%5B7%5D%5Bamount%5D=100000&payment_types%5B6%5D%5Bamount%5D=100000&_pjax=%23worker-payment-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1752316492.0137;s:10:"statusCode";i:200;s:8:"sqlCount";i:90;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10898048;s:14:"processingTime";d:0.8495900630950928;}s:10:"exceptions";a:0:{}}